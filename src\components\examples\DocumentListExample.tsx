import React, { useState } from "react";
import { useDocumentByProjectIdWithUserRole } from "@/hooks/queries/document";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { formatDateTime } from "@/utils";
import { Loader2, FileText } from "lucide-react";

interface DocumentListExampleProps {
  projectId: string;
}

/**
 * Example component demonstrating how to use the combined 
 * useDocumentByProjectIdWithUserRole hook
 * 
 * This hook returns data in the format:
 * {
 *   "page-index": 1,
 *   "page-size": 10,
 *   "total-count": 3,
 *   "total-page": 1,
 *   "data-list": [
 *     {
 *       "id": "...",
 *       "name": "CV",
 *       "type": "ScienceCV",
 *       "is-template": false,
 *       "content-html": "...",
 *       "updated-at": "2025-08-05T11:55:28.7837145",
 *       "upload-at": "2025-08-05T11:55:28.7837155",
 *       "status": "created",
 *       "uploader-id": "...",
 *       "account-id": "...",
 *       "full-name": "Doan Huynh Tuong Vy (K17 HCM)",
 *       "avatar-url": "https://...",
 *       "project-id": "...",
 *       "evaluation-id": null,
 *       "individual-evaluation-id": null,
 *       "transaction-id": null
 *     }
 *   ]
 * }
 */
const DocumentListExample: React.FC<DocumentListExampleProps> = ({ projectId }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);

  // Use the combined hook that fetches documents with user role data
  const { data: documentsResponse, isLoading, error } = useDocumentByProjectIdWithUserRole(
    {
      "is-template": false,
      status: "created",
      "page-index": currentPage,
      "page-size": pageSize,
      "project-id": projectId,
    },
    !!projectId
  );

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <Loader2 className="w-6 h-6 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8 text-red-500">
        Error loading documents: {error.message}
      </div>
    );
  }

  // Extract data from the response
  const documents = documentsResponse?.["data-list"] || [];
  const totalCount = documentsResponse?.["total-count"] || 0;
  const totalPages = documentsResponse?.["total-page"] || 1;

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-semibold">Documents with User Info</h2>
        <p className="text-sm text-muted-foreground">
          Total: {totalCount} documents
        </p>
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Document</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Upload Date</TableHead>
            <TableHead>Uploader</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {documents.map((document) => (
            <TableRow key={document.id}>
              <TableCell>
                <div className="flex items-center space-x-2">
                  <FileText className="w-4 h-4 text-blue-600" />
                  <div>
                    <p className="font-medium text-sm">{document.name}</p>
                    <Badge variant="outline" className="text-xs">
                      {document.status}
                    </Badge>
                  </div>
                </div>
              </TableCell>
              <TableCell>{document.type}</TableCell>
              <TableCell>{formatDateTime(document["upload-at"])}</TableCell>
              <TableCell>
                <div className="flex items-center space-x-2">
                  {document["avatar-url"] && (
                    <img
                      src={document["avatar-url"]}
                      alt={document["full-name"] || "User"}
                      className="w-6 h-6 rounded-full"
                    />
                  )}
                  <span className="text-sm">
                    {document["full-name"] || "Unknown User"}
                  </span>
                </div>
              </TableCell>
              <TableCell>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => console.log("View document:", document)}
                >
                  View
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Simple pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(currentPage - 1)}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          <span className="flex items-center px-3 text-sm">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
};

export default DocumentListExample;
