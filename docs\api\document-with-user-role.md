# Document with User Role API

This document explains how to use the combined API function that fetches documents by project ID and includes user role information for each document.

## Overview

The `getDocumentByProjectIdWithUserRole` function combines two API calls:
1. `getDocumentByProjectId` - Fetches documents by project ID
2. `getUserRoleById` - Fetches user role information for each document's uploader

## API Structure

Following the pattern: **type -> server -> query -> Component**

### 1. Type Definition

```typescript
// src/types/document.ts
export interface DocumentListWithUserRoleResponse {
  "page-index": number;
  "page-size": number;
  "total-count": number;
  "total-page": number;
  "data-list": DocumentWithUserRole[];
}

export interface DocumentWithUserRole extends DocumentForm {
  "account-id"?: string;
  "full-name"?: string;
  "avatar-url"?: string;
}
```

### 2. Server Function

```typescript
// src/services/resources/document.ts
export const getDocumentByProjectIdWithUserRole = async (
  request: GetDocumentByProjectIdRequest
): Promise<DocumentListWithUserRoleResponse>
```

### 3. Query Hook

```typescript
// src/hooks/queries/document.ts
export function useDocumentByProjectIdWithUserRole(
  request: GetDocumentByProjectIdRequest,
  enabled: boolean = true
)
```

### 4. Component Usage

```typescript
// In your component
const { data, isLoading, error } = useDocumentByProjectIdWithUserRole({
  "is-template": false,
  status: "created",
  "page-index": 1,
  "page-size": 10,
  "project-id": "your-project-id",
});
```

## Response Format

The function returns data in the following format:

```json
{
  "page-index": 1,
  "page-size": 10,
  "total-count": 3,
  "total-page": 1,
  "data-list": [
    {
      "id": "f68a8660-02b5-40b8-8911-688d8124343d",
      "name": "CV",
      "type": "ScienceCV",
      "is-template": false,
      "content-html": "<html>...</html>",
      "updated-at": "2025-08-05T11:55:28.7837145",
      "upload-at": "2025-08-05T11:55:28.7837155",
      "status": "created",
      "uploader-id": "a656ab75-f50c-4a28-b371-771d5fd11c29",
      "account-id": "37922e4f-3000-46e8-9dfb-7b11e1e0050c",
      "full-name": "Doan Huynh Tuong Vy (K17 HCM)",
      "avatar-url": "https://lh3.googleusercontent.com/...",
      "project-id": "ba57ff7f-7033-48a4-8ec8-057c5a491d0a",
      "evaluation-id": null,
      "individual-evaluation-id": null,
      "transaction-id": null
    }
  ]
}
```

## Usage Examples

### Basic Usage

```typescript
import { useDocumentByProjectIdWithUserRole } from "@/hooks/queries/document";

const MyComponent = ({ projectId }: { projectId: string }) => {
  const { data, isLoading, error } = useDocumentByProjectIdWithUserRole({
    "is-template": false,
    status: "created",
    "page-index": 1,
    "page-size": 10,
    "project-id": projectId,
  });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  const documents = data?.["data-list"] || [];
  
  return (
    <div>
      {documents.map(doc => (
        <div key={doc.id}>
          <h3>{doc.name}</h3>
          <p>Uploaded by: {doc["full-name"]}</p>
          {doc["avatar-url"] && <img src={doc["avatar-url"]} alt="Avatar" />}
        </div>
      ))}
    </div>
  );
};
```

### With Pagination

```typescript
const [currentPage, setCurrentPage] = useState(1);
const [pageSize, setPageSize] = useState(10);

const { data, isLoading } = useDocumentByProjectIdWithUserRole({
  "is-template": false,
  status: "created",
  "page-index": currentPage,
  "page-size": pageSize,
  "project-id": projectId,
});

const totalPages = data?.["total-page"] || 1;
const totalCount = data?.["total-count"] || 0;
```

## Benefits

1. **Single Hook**: One hook call instead of multiple API calls
2. **Automatic Data Combination**: User role data is automatically merged with document data
3. **Error Handling**: Graceful fallback if user role data fails to load
4. **Caching**: React Query caching for better performance
5. **Reusable**: Can be used across multiple components

## Error Handling

The function includes built-in error handling:
- If `getUserRoleById` fails for a document, it returns the document with undefined user role fields
- The main document list will still be returned even if some user role calls fail
- Errors are logged to console for debugging

## Performance Considerations

- Uses `Promise.all` for concurrent user role API calls
- Includes 5-minute cache time for user role data
- Graceful degradation if user role API is unavailable

## Migration from Separate Calls

If you're currently using separate hooks, you can migrate like this:

**Before:**
```typescript
const { data: docs } = useDocumentByProjectId(request);
// Multiple useUserRoleById calls for each document
```

**After:**
```typescript
const { data } = useDocumentByProjectIdWithUserRole(request);
const documents = data?.["data-list"] || [];
// User role data is already included in each document
```
